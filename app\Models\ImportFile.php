<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ImportFile extends Model
{
    use HasFactory;

    const STATUS_NEW = 'New';
    const STATUS_SIGNED = 'Signed'; // Kept for backward compatibility
    const STATUS_PENDING_APPROVAL = 'Pending Approval';
    const STATUS_PENDING_DISPATCH = 'Pending Dispatch';
    const STATUS_PENDING_REVISION = 'Pending Revision';
    const STATUS_SENT = 'Sent';
    const STATUS_SENT_FOR_VOIDING = 'Sent for Voiding';
    const STATUS_VOIDED = 'Voided';

    //TABLE
    public $table = 'import_files';

    //FILLABLES
    protected $fillable = [
        'import_id',
        'file_name',
        'file_path',
        'script_date',
        'last_name',
        'first_name',
        'dob',
        'gender',
        'address',
        'city',
        'state',
        'zip',
        'phone',
        'medication',
        'refills',
        'vial_quantity',
        'days_supply',
        'sig',
        'notes',
        'ship_to',
        'status',
        'number',
        'signed_at',
        'sent_at',
        'comment',
        'returned_by_user_id',
        'voided_by_user_id',
        'is_eligible_for_signing',
        'order_id',
        'patient_id'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        // 'script_date' => 'date',
        // 'dob' => 'date', // Removed to handle formatting in accessor
        'signed_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function import()
    {
        return $this->belongsTo(Import::class);
    }

    public function returnedByUser()
    {
        return $this->belongsTo(User::class, 'returned_by_user_id');
    }

    public function voidedByUser()
    {
        return $this->belongsTo(User::class, 'voided_by_user_id');
    }


    public function medication()
    {
        return $this->belongsTo(Medication::class, 'name', 'medication');
    }

    //ATTRIBUTES
    /**
     * Get the DOB attribute formatted for HTML date inputs
     */
    public function getDobAttribute($value)
    {
        if ($value) {
            return \Carbon\Carbon::parse($value)->format('Y-m-d');
        }
        return $value;
    }

    /**
     * Set the DOB attribute from HTML date inputs
     */
    public function setDobAttribute($value)
    {
        if ($value) {
            $this->attributes['dob'] = \Carbon\Carbon::parse($value)->format('Y-m-d');
        } else {
            $this->attributes['dob'] = null;
        }
    }

    /**
     * Get the script_date attribute formatted for HTML date inputs
     */
    public function getScriptDateAttribute($value)
    {
        if ($value) {
            return \Carbon\Carbon::parse($value)->format('Y-m-d');
        }
        return $value;
    }

    /**
     * Set the script_date attribute from HTML date inputs
     */
    public function setScriptDateAttribute($value)
    {
        if ($value) {
            $this->attributes['script_date'] = \Carbon\Carbon::parse($value)->format('Y-m-d');
        } else {
            $this->attributes['script_date'] = null;
        }
    }

    // public function getFilePathAttribute($value)
    // {
    //     if (strpos($value, 'public/') === 0) {
    //         $value = substr($value, 7);
    //     }
    //     return $value;
    // }
}
