<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'text' => '',
    'route' => '',
    'active' => null,
    'icon' => null,
    'count' => 0
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'text' => '',
    'route' => '',
    'active' => null,
    'icon' => null,
    'count' => 0
]); ?>
<?php foreach (array_filter(([
    'text' => '',
    'route' => '',
    'active' => null,
    'icon' => null,
    'count' => 0
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<li class="menu-item
    <?php if(isset($active) && $active && strpos(Route::currentRouteName(), $active) !== false): ?>
        menu-item-active
    <?php elseif(Route::currentRouteName() == $route): ?>
        menu-item-active
    <?php endif; ?>
">
    <a href="<?php echo e($route ? route($route) : '#'); ?>" class="menu-link">
        <i class="<?php echo e($icon ? 'menu-icon '.$icon : 'menu-bullet menu-bullet-dot'); ?>"><span></span></i>
        <span class="menu-text"><?php echo e($text); ?></span>
        <?php if($count > 0): ?>
            <span class="menu-count"><?php echo e($count); ?></span>
        <?php else: ?>
            <span class="menu-count">0</span>
        <?php endif; ?>
    </a>
</li>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/components/aside-item-with-count.blade.php ENDPATH**/ ?>