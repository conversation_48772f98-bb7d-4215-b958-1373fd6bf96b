<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['label', 'labelRequired', 'model' => null]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['label', 'labelRequired', 'model' => null]); ?>
<?php foreach (array_filter((['label', 'labelRequired', 'model' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<label <?php echo e($attributes); ?>>
    <?php echo e($label); ?>

    <?php if($labelRequired ?? false): ?>
        <span class="text-danger">*</span>
    <?php endif; ?>
</label><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/components/form/label.blade.php ENDPATH**/ ?>