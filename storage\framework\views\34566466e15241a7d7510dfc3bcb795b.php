<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'steps' => [],
    'currentStep' => 1,
    'totalSteps' => 1,
    'editMode' => false,
    'gap' => '4rem',
    'showStepIndicator' => true,
    'showNavigation' => true,
    'prevButtonText' => 'Previous',
    'nextButtonText' => 'Next',
    'submitButtonText' => null,
    'prevButtonClass' => 'btn btn-secondary',
    'nextButtonClass' => 'btn btn-primary',
    'submitButtonClass' => 'btn btn-success'
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'steps' => [],
    'currentStep' => 1,
    'totalSteps' => 1,
    'editMode' => false,
    'gap' => '4rem',
    'showStepIndicator' => true,
    'showNavigation' => true,
    'prevButtonText' => 'Previous',
    'nextButtonText' => 'Next',
    'submitButtonText' => null,
    'prevButtonClass' => 'btn btn-secondary',
    'nextButtonClass' => 'btn btn-primary',
    'submitButtonClass' => 'btn btn-success'
]); ?>
<?php foreach (array_filter(([
    'steps' => [],
    'currentStep' => 1,
    'totalSteps' => 1,
    'editMode' => false,
    'gap' => '4rem',
    'showStepIndicator' => true,
    'showNavigation' => true,
    'prevButtonText' => 'Previous',
    'nextButtonText' => 'Next',
    'submitButtonText' => null,
    'prevButtonClass' => 'btn btn-secondary',
    'nextButtonClass' => 'btn btn-primary',
    'submitButtonClass' => 'btn btn-success'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div>
    <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <div class="col">
            <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <?php if($showStepIndicator): ?>
                    <!-- Step Progress Indicator -->
                    <?php if (isset($component)) { $__componentOriginal54fde6bcdc75a1fc2087289d7f9c55f1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal54fde6bcdc75a1fc2087289d7f9c55f1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.multistep.step-indicator','data' => ['steps' => $steps,'currentStep' => $currentStep,'editMode' => $editMode,'gap' => $gap]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('multistep.step-indicator'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['steps' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($steps),'currentStep' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($currentStep),'editMode' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($editMode),'gap' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($gap)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal54fde6bcdc75a1fc2087289d7f9c55f1)): ?>
<?php $attributes = $__attributesOriginal54fde6bcdc75a1fc2087289d7f9c55f1; ?>
<?php unset($__attributesOriginal54fde6bcdc75a1fc2087289d7f9c55f1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal54fde6bcdc75a1fc2087289d7f9c55f1)): ?>
<?php $component = $__componentOriginal54fde6bcdc75a1fc2087289d7f9c55f1; ?>
<?php unset($__componentOriginal54fde6bcdc75a1fc2087289d7f9c55f1); ?>
<?php endif; ?>
                <?php endif; ?>

                <!-- Step Content -->
                <?php echo e($slot); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>

            <?php if($showNavigation): ?>
                <?php if (isset($component)) { $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <div class="d-flex justify-content-between">
                        <?php if($currentStep > 1): ?>
                            <button type="button" class="<?php echo e($prevButtonClass); ?>" wire:click="prevStep" wire:loading.attr="disabled">
                                <i class="fas fa-arrow-left me-1"></i> <?php echo e($prevButtonText); ?>

                            </button>
                        <?php else: ?>
                            <div></div>
                        <?php endif; ?>

                        <?php if($currentStep < $totalSteps): ?>
                            <button type="button" class="<?php echo e($nextButtonClass); ?>" wire:click="nextStep" wire:loading.attr="disabled">
                                <?php echo e($nextButtonText); ?> <i class="fas fa-arrow-right ms-1"></i>
                            </button>
                        <?php else: ?>
                            <button type="button" class="<?php echo e($submitButtonClass); ?>" wire:click="submit" wire:loading.attr="disabled" wire:loading.class="disabled">
                                <span wire:loading.remove wire:target="submit">
                                    <i class="fas fa-save me-1"></i> <?php echo e($submitButtonText ?? ($editMode ? 'Update' : 'Create')); ?>

                                </span>
                                <span wire:loading wire:target="submit">
                                    <i class="fas fa-spinner fa-spin me-1"></i> <?php echo e($editMode ? 'Updating...' : 'Creating...'); ?>

                                </span>
                            </button>
                        <?php endif; ?>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $attributes = $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $component = $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
            <?php endif; ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/components/multistep/form-wrapper.blade.php ENDPATH**/ ?>