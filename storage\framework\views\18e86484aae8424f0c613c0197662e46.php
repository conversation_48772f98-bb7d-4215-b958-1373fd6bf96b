<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'borderTop' => false,
    'borderBottom' => false,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'borderTop' => false,
    'borderBottom' => false,
]); ?>
<?php foreach (array_filter(([
    'borderTop' => false,
    'borderBottom' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="card-body <?php if($borderTop): ?> border-top <?php endif; ?> <?php if($borderBottom): ?> border-bottom <?php endif; ?>">
    <?php echo e($slot); ?>

</div>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/components/card/body.blade.php ENDPATH**/ ?>