<?php

namespace App\Helpers;

use App\Models\Patient;
use App\Models\State;
use Carbon\Carbon;

class PatientHelper
{

    /**
     * Check if a patient exists and return the patient or create a new one
     *
     * @param int $providerId
     * @param string $firstName
     * @param string $lastName
     * @param string $dob
     * @return array
     */
    public static function findOrCreatePatient($providerId, $firstName, $lastName, $dob)
    {
        // $dob = Carbon::parse($dob)->format('Y-m-d');
        $query = Patient::where('provider_id', $providerId)
            ->where('first_name', $firstName)
            ->where('last_name', $lastName)
            ->where('dob', $dob);

        $patient = $query->first();

        if ($patient) {
            return [
                'exists' => true,
                'patient' => $patient
            ];
        }

        $newPatient = Patient::create([
            'provider_id' => $providerId,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'dob' => $dob,
            'state_id' => null
        ]);

        return [
            'exists' => false,
            'patient' => $newPatient
        ];
    }

    /**
     * Check if a patient exists and return the patient or create a new one
     *
     * @param array $patientData
     * @return array
     */

    public static function findUpdateOrCreate($patientData)
    {
        if (!is_numeric($patientData['state'])) {
            $state = State::where('short_name', $patientData['state'])->firstOrFail();
        } else {
            $state = State::find($patientData['state']);
        }

        $patient = Patient::updateOrCreate(
            [
                'provider_id' => $patientData['provider_id'],
                'first_name' => $patientData['first_name'],
                'last_name' => $patientData['last_name'],
                'dob' => $patientData['dob']
            ],
            [
                'state_id' => $state->id ?? null,
                'phone_number' => $patientData['phone_number'],
                'gender' => $patientData['gender'],
                'city' => $patientData['city'],
                'zip_code' => $patientData['zip_code'],
                'address' => $patientData['address']
            ]
        );
        
        return [
            'newlyCreated' => $patient->wasRecentlyCreated,
            'patient' => $patient
        ];
    }
}
