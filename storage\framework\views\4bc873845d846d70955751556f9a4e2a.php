<!DOCTYPE html>
<html lang="en">

<head>

    <title><?php echo e(config('app.name')); ?> | <?php echo $__env->yieldContent('title', $page_title ?? ''); ?></title>

    <meta charset="utf-8" />
    <meta name="description" content="Default minimized aside" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
    <?php if(auth()->guard()->check()): ?>
        <meta name="user-role" content="<?php echo e(Auth::user()->role); ?>" />
    <?php endif; ?>

    <link href="<?php echo e(asset('plugins/global/plugins.bundle.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/style.bundle.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/themes/layout/header/base/light.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/themes/layout/header/menu/light.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/themes/layout/brand/light.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/themes/layout/aside/light.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/common.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/black-theme.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/custom-header.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/custom-tooltips.css')); ?>" rel="stylesheet" type="text/css" />

    <link rel="shortcut icon" href="<?php echo e(asset('images/logo.png')); ?>" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">

    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />

    
    <?php echo $__env->yieldContent('styles'); ?>
    <?php echo $__env->yieldPushContent('styles'); ?>

    
    <style>
        .page-loading .progress-steps-container {
            display: none !important;
        }
    </style>
</head>

<body id="kt_body"
    class="header-fixed header-mobile-fixed subheader-enabled subheader-fixed aside-enabled aside-fixed aside-hoverable aside-minimize-hoverable page-loading">



    <div id="kt_header_mobile" class="header-mobile align-items-center header-mobile-fixed">
        <a href="<?php echo e(route('dashboard')); ?>">
            <h3 class="m-0 text-dark"><?php echo e(config('app.name')); ?></h3>
        </a>

        <div class="d-flex align-items-center">
            <button class="btn p-0 burger-icon burger-icon-left" id="kt_aside_mobile_toggle">
                <span></span>
            </button>
            
            <button class="btn btn-hover-text-primary p-0 ml-2" id="kt_header_mobile_topbar_toggle">
                <span class="svg-icon svg-icon-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                        height="24px" viewBox="0 0 24 24" version="1.1">
                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <polygon points="0 0 24 0 24 24 0 24" />
                            <path
                                d="M12,11 C9.790861,11 8,9.209139 8,7 C8,4.790861 9.790861,3 12,3 C14.209139,3 16,4.790861 16,7 C16,9.209139 14.209139,11 12,11 Z"
                                fill="#000000" fill-rule="nonzero" opacity="0.3" />
                            <path
                                d="M3.00065168,20.1992055 C3.38825852,15.4265159 7.26191235,13 11.9833413,13 C16.7712164,13 20.7048837,15.2931929 20.9979143,20.2 C21.0095879,20.3954741 20.9979143,21 20.2466999,21 C16.541124,21 11.0347247,21 3.72750223,21 C3.47671215,21 2.97953825,20.45918 3.00065168,20.1992055 Z"
                                fill="#000000" fill-rule="nonzero" />
                        </g>
                    </svg>
                    <!--end::Svg Icon-->
                </span>
            </button>
            <!--end::Topbar Mobile Toggle-->
        </div>
        <!--end::Toolbar-->
    </div>

    <?php echo $__env->make('layouts.aside', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <div class="d-flex flex-column flex-row-fluid wrapper" id="kt_wrapper">
        <?php echo $__env->make('layouts.head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <div class="subheader py-2 subheader-solid " id="kt_subheader">
            <div class=" container-fluid  d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
                <div class="d-flex align-items-center flex-wrap mr-1">
                    <h5 class="text-dark font-weight-bold my-2 mr-5">
                        <?php if($has_back ?? ''): ?>
                            <a class="btn btn-link btn-hover-light-primary" href="<?php echo e($has_back); ?>"><i
                                    class="fa fa-arrow-left pr-0"></i></a>
                        <?php endif; ?> <?php echo $__env->yieldContent('title', $page_title ?? ''); ?>
                    </h5>
                </div>
            </div>
        </div>

        <div class="d-flex flex-column-fluid mt-5">
            <div class="container-fluid">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>


    
    <div id="kt_scrolltop" class="scrolltop">
        <span class="svg-icon">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px"
                height="24px" viewBox="0 0 24 24" version="1.1">
                <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                    <polygon points="0 0 24 0 24 24 0 24" />
                    <rect fill="#000000" opacity="0.3" x="11" y="10" width="2" height="10"
                        rx="1" />
                    <path
                        d="M6.70710678,12.7071068 C6.31658249,13.0976311 5.68341751,13.0976311 5.29289322,12.7071068 C4.90236893,12.3165825 4.90236893,11.6834175 5.29289322,11.2928932 L11.2928932,5.29289322 C11.6714722,4.91431428 12.2810586,4.90106866 12.6757246,5.26284586 L18.6757246,10.7628459 C19.0828436,11.1360383 19.1103465,11.7686056 18.7371541,12.1757246 C18.3639617,12.5828436 17.7313944,12.6103465 17.3242754,12.2371541 L12.0300757,7.38413782 L6.70710678,12.7071068 Z"
                        fill="#000000" fill-rule="nonzero" />
                </g>
            </svg>
        </span>
    </div>
    
    <script src="<?php echo e(asset('js/app.js')); ?>"></script>

    <script src="<?php echo e(asset('js/init_common.js')); ?>"></script>

    <script src="<?php echo e(asset('plugins/global/plugins.bundle.js')); ?>"></script>

    <script src="<?php echo e(asset('js/scripts.bundle.js')); ?>"></script>

    <script src="<?php echo e(asset('js/datatables.bundle.js')); ?>"></script>

    <script src="<?php echo e(asset('js/common.js')); ?>"></script>

    <script src="<?php echo e(asset('js/toastr-init.js')); ?>"></script>

    <!-- Device Time Capture Script -->
    <script src="<?php echo e(asset('js/device-time-capture.js')); ?>"></script>

    <!-- Config Helper for JavaScript -->
    <script src="<?php echo e(asset('js/config-helper.js')); ?>"></script>
    <script>
        // Initialize app configuration
        window.appConfig = {
            broadcasting: {
                connections: {
                    pusher: {
                        channels: {
                            scripts: "<?php echo e(config('broadcasting.connections.pusher.channels.scripts')); ?>"
                        }
                    }
                }
            },
            app: {
                staffPortalDomain: "<?php echo e(env('STAFF_PORTAL_DOMAIN')); ?>"
            }
        };
    </script>

    <!-- Pusher and Laravel Echo for real-time notifications -->
    <script src="https://js.pusher.com/7.0/pusher.min.js"></script>
    <script>
        // Initialize Pusher if Echo is not already defined
        if (typeof window.Echo === 'undefined') {
            window.Pusher = Pusher;
            window.Echo = new Echo({
                broadcaster: 'pusher',
                key: '<?php echo e(env('PUSHER_APP_KEY')); ?>',
                cluster: '<?php echo e(env('PUSHER_APP_CLUSTER')); ?>',
                forceTLS: true
            });
        }
    </script>

    <!-- Script Notifications -->
    <script src="<?php echo e(asset('js/script-notifications.js')); ?>"></script>

    <script>
        <?php if(session()->has('success-message')): ?>
            toastr.success("<?php echo e(session('success-message')); ?>")
        <?php endif; ?>
        <?php if(session()->has('error-message')): ?>
            toastr.error("<?php echo e(session('error-message')); ?>")
        <?php endif; ?>
        <?php if(session()->has('info-message')): ?>
            toastr.info("<?php echo e(session('info-message')); ?>")
        <?php endif; ?>
        <?php if(session()->has('warning-message')): ?>
            toastr.warning("<?php echo e(session('warning-message')); ?>")
        <?php endif; ?>
    </script>

    <!-- Global Tooltip Initialization -->
    <script>
        $(document).ready(function() {
            // Initialize tooltips for all elements with data-toggle="tooltip"
            function initializeTooltips() {
                // First, destroy any existing tooltips to prevent duplicates
                $('[data-toggle="tooltip"]').tooltip('dispose');

                // Wait for DOM to be fully ready before initializing
                setTimeout(function() {
                    // Initialize new tooltips with proper configuration
                    $('[data-toggle="tooltip"]').tooltip({
                        container: 'body',
                        template: '<div class="tooltip tooltip-purple" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',
                        trigger: 'hover focus',
                        placement: function(context, source) {
                            // Dynamic placement based on element position
                            const $source = $(source);
                            const offset = $source.offset();
                            const windowHeight = $(window).height();
                            const scrollTop = $(window).scrollTop();

                            // If element is in the top half of the viewport, show tooltip below
                            if (offset.top - scrollTop < windowHeight / 2) {
                                return 'bottom';
                            }
                            return 'top';
                        },
                        delay: {
                            show: 150,
                            hide: 100
                        },
                        boundary: 'viewport',
                        fallbackPlacement: ['bottom', 'top', 'right', 'left'],
                        animation: false, // Disable animation to prevent positioning issues
                        html: true,
                        sanitize: true,
                        offset: '0, 8'
                    });
                }, 50);
            }

            // Initialize tooltips on page load with a small delay to ensure DOM is ready
            setTimeout(function() {
                initializeTooltips();
            }, 100);

            // Re-initialize tooltips after AJAX content loads (for datatables)
            $(document).on('DOMNodeInserted', function(e) {
                if ($(e.target).find('[data-toggle="tooltip"]').length || $(e.target).is(
                        '[data-toggle="tooltip"]')) {
                    setTimeout(function() {
                        initializeTooltips();
                    }, 150);
                }
            });

            // Re-initialize tooltips when datatables are reloaded
            $(document).on('datatable-on-ajax-done', function() {
                setTimeout(function() {
                    initializeTooltips();
                }, 300);
            });

            // Handle page visibility changes to reinitialize tooltips
            $(document).on('visibilitychange', function() {
                if (!document.hidden) {
                    setTimeout(function() {
                        initializeTooltips();
                    }, 100);
                }
            });

            // Reinitialize tooltips when window is resized
            $(window).on('resize', function() {
                setTimeout(function() {
                    initializeTooltips();
                }, 100);
            });

            // Hide tooltip on mouseleave
            $(document).on('mouseleave', '[data-toggle="tooltip"]', function() {
                $(this).tooltip('hide');
            });
            // Dispose tooltip on DOM removal
            $(document).on('DOMNodeRemoved', '[data-toggle="tooltip"]', function() {
                $(this).tooltip('dispose');
            });
        });
    </script>
    <?php echo $__env->yieldContent('scripts'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <script>
        if (window.livewire) {
            window.livewire.on('alert', param => {
                toastr[param['type']](param['message']);
            });
        }
    </script>

</body>

</html>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/master.blade.php ENDPATH**/ ?>