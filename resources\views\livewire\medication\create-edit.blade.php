<div>
    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <div class="row">

                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Medication Title" labelRequired="1" model="medication.name" />
                        </div>

                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="NDC (National Drug Code)" labelRequired="1"
                                model="medication.ndc" />
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="DispensePro Medication Name" labelRequired="1" model="medication.dispensepro_medication_name" />
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Refills" labelRequired="0" model="medication.refills" type="number"/>
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Vial Quantity" labelRequired="0"
                                model="medication.vial_quantity" type="number"/>
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Days Supply" labelRequired="0" model="medication.days_supply"
                                type="number"/>
                        </div>

                        <!-- Additional Information -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <x-form.input.textarea label="SIG" labelRequired="0"
                                        model="medication.sig" />
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <x-form.input.textarea label="Note" labelRequired="0"
                                        model="medication.notes" rows="3" />
                                </div>
                            </div>
                    </div>
                </x-card.body>

                <x-group.errors />

                <x-card.footer>
                    <x-btn-with-loading target="store" text="Save" type="submit" />
                </x-card.footer>
            </div>
        </x-layout.row>
    </form>
</div>

@push('scripts')
    <script>
        document.addEventListener("livewire:load", function() {
            function createRoleDropdown() {
                $('#user\\.role').select2({
                    placeholder: "Select Access Type",
                    minimumResultsForSearch: Infinity, // Disable the search functionality
                }).on('change', function(e) {
                    @this.set('user.role', $(e.target).val());
                });
            }

            createRoleDropdown();

            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createRoleDropdown();
            });
        });
    </script>
@endpush
