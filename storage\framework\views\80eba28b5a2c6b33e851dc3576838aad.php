

<?php
    use App\Models\ImportFile;
?>

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-auto">
                    <button type="button" id="download-all-global-btn" class="btn btn-dark">
                        <i class="fa fa-download mr-1"></i> Download All
                    </button>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <label for="signed_date_filter">Signed Date:</label>
                    <div class="input-group date">
                        <input type="date" class="form-control" id="signed_date_filter" max="<?php echo e(date('Y-m-d')); ?>" />
                        <div class="input-group-append">
                            <button class="btn btn-secondary" type="button" id="clear_date_filter">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="provider_filter">Provider:</label>
                    <select class="form-control" id="provider_filter">
                        <option value="">All Providers</option>
                        <?php $__currentLoopData = $providers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($provider->id); ?>"><?php echo e($provider->first_name); ?> <?php echo e($provider->last_name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="clinic_name_filter">Clinic:</label>
                    <select class="form-control" id="clinic_name_filter">
                        <option value="">All Clinics</option>
                        <?php if(isset($clinic_names)): ?>
                            <?php $__currentLoopData = $clinic_names; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $clinic): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($clinic); ?>"><?php echo e($clinic); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="medication_filter">Medication:</label>
                    <select class="form-control" id="medication_filter">
                        <option value="">All Medications</option>
                        <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($medication->id); ?>"><?php echo e($medication->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="users_dt"></div>

        </div>
    </div>

    <!-- Script Preview Modal -->
    <div class="modal fade" id="scriptPreviewModal" tabindex="-1" role="dialog" aria-labelledby="scriptPreviewModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scriptPreviewModalLabel">Script Preview</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i class="ki ki-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="script-preview-content">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" id="download-preview-btn" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download
                    </a>
                    <button type="button" id="send-preview-btn" class="btn btn-success">
                        <i class="fas fa-paper-plane"></i> Send
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Return Script Modal -->
    <div class="modal fade" id="returnScriptModal" tabindex="-1" role="dialog" aria-labelledby="returnScriptModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="returnScriptModalLabel">Return Script</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="return-reason">Reason for return:</label>
                        <textarea class="form-control" id="return-reason" placeholder="Enter reason..." rows="4"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirm-return-script-btn">Return Script</button>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
    <?php echo \Illuminate\View\Factory::parentPlaceholder('styles'); ?>
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        button:disabled {
            cursor: not-allowed !important;
        }

        .text-primary {
            text-decoration: none;
        }

        .text-primary:hover {
            text-decoration: underline;
        }

        /* Custom styles for the preview modal */
        #scriptPreviewModal .modal-dialog {
            max-width: 95%;
            height: 95vh;
            margin: 0.5rem auto;
        }

        #scriptPreviewModal .modal-content {
            height: 100%;
            border-radius: 4px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        #scriptPreviewModal .modal-body {
            flex: 1;
            overflow: hidden;
            padding: 0;
        }

        #scriptPreviewModal .modal-header {
            border-bottom: 1px solid #ebedf3;
            padding: 1rem 1.75rem;
        }

        #scriptPreviewModal .modal-footer {
            border-top: 1px solid #ebedf3;
            padding: 1rem 1.75rem;
            position: relative;
            flex-shrink: 0;
            justify-content: flex-end;
            background-color: #fff;
            z-index: 5;
        }

        #scriptPreviewModal .close {
            cursor: pointer;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            margin: 0;
            padding: 0;
        }

        #scriptPreviewModal .close i {
            font-size: 1rem;
        }

        #scriptPreviewModal .close:hover {
            color: #3699FF;
            background-color: #f3f6f9;
            border-radius: 4px;
        }

        #script-preview-content {
            height: 100%;
            width: 100%;
            overflow: hidden;
            position: relative;
        }

        #script-preview-content iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        .select2-container .select2-selection--single {
            height: 38px;
            display: flex;
            align-items: center;
            padding: 6px 12px;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            height: 100%;
            display: flex;
            align-items: center;
            padding-left: 8px;
            /* optional, spacing from text */
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <?php echo \Illuminate\View\Factory::parentPlaceholder('scripts'); ?>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#provider_filter').select2({
                width: '100%'
            });
            $('#clinic_name_filter').select2({
                width: '100%'
            });
            $('#medication_filter').select2({
                width: '100%'
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `<?php echo e(url('/storage')); ?>`;
        const apiRoute = `<?php echo e(route('scripts.api.all')); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";
        const deleteRoute = `<?php echo e(route('scripts.delete', ['importFile' => '::ID'])); ?>`;
        const viewRoute = `<?php echo e(route('scripts.preview', ['importId' => '::ID'])); ?>`;
        const downloadRoute = `<?php echo e(route('scripts.download-all-pdf', ['importId' => '::ID'])); ?>`;
        const downloadSingleRoute = `<?php echo e(route('archive.file-download', ['id' => '::ID'])); ?>`;
        const signRoute = `<?php echo e(route('archive.sign-pdf', ['id' => '::ID'])); ?>`;
        const sendRoute = `<?php echo e(route('archive.send-fax', ['id' => '::ID'])); ?>`;
        const userEditRoute = `<?php echo e(route('users.edit', ['user' => '::ID'])); ?>`;
        const returnRoute = `<?php echo e(route('scripts.api.return-script')); ?>`;


        datatableElement = $('#users_dt');
        searchElement = $('#users_search');

        columnArray = [{
                field: 'import_file_name',
                title: `File Name`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return `<div style="white-space: normal; text-wrap: wrap;">${data.import_file_name ?? ''}</div>`;
                }
            },
            {
                field: 'created_at',
                title: `Created At`,
                autoHide: false,
                sortable: true,
                width: 'auto',
                template: function(data) {
                    return data.created_at ? moment(data.created_at).format('MM/DD/YYYY  hh:mm A') : '';
                }
            },
            {
                field: 'signed_at',
                title: `Signed at`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.signed_at ? moment.parseZone(data.signed_at).format('MM/DD/YYYY hh:mm A') :
                        '<b>Not Signed Yet</b>';
                }
            },
            {
                field: 'sent_at',
                title: `Sent at`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.sent_at ? moment(data.sent_at).format('MM/DD/YYYY hh:mm A') :
                        '<b>Not Sent Yet</b>';
                }
            },
            {
                field: 'script_date',
                title: `Script date`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return moment(data.script_date).format('MM/DD/YYYY');
                }
            },
            {
                field: 'last_name',
                title: `Last name`,

                sortable: true,
                autoHide: false
            },
            {
                field: 'first_name',
                title: `First name`,

                sortable: true,
                autoHide: false
            },
            {
                field: 'medication',
                title: `Medication`,

                sortable: true,
                autoHide: false
            },
            {
                field: 'provider_name',
                title: `Provider Name`,
                width: 'auto',
                sortable: true,
                autoHide: false,
                template: function(data) {
                    if (data.import && data.import.user && data.import.user.id) {
                        return `<a href="${userEditRoute.replace('::ID', data.import.user.id)}" class="text-primary font-weight-bold" data-toggle="tooltip" title="Edit Provider">
                ${data.provider_name}
            </a>`;
                    } else {
                        return data.provider_name || '';
                    }
                }
            },
            {
                field: 'status',
                title: `Status`,
                width: 'auto',
                sortable: true,
                autoHide: false
            },
            {
                field: 'Actions',
                title: 'Actions',
                width: 'auto',
                sortable: false,
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    const downloadBtn = `
            <a href="${downloadSingleRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Download Script">
                <i class="menu-icon fas fa-download"></i>
            </a>`;

                    const signBtn = '';

                    const sendBtn = data.status == '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>' ? `
            <a href="${sendRoute.replace('::ID', data.id)}" data-id="${data.id}" class="btn btn-sm btn-clean btn-icon send-btn" data-toggle="tooltip" title="Send Script">
                <i class="menu-icon fas fa-paper-plane"></i>
            </a>` : '';

                    const viewBtn = `
            <a href="#" data-id="${data.id}"
               data-view-route="${viewRoute.replace('::ID', data.id)}"
               data-download-route="${downloadSingleRoute.replace('::ID', data.id)}"
               data-send-route="${sendRoute.replace('::ID', data.id)}"
               class="btn btn-sm btn-clean btn-icon preview-btn" data-toggle="tooltip" title="View Script">
                <i class="menu-icon fas fa-eye"></i>
            </a>`;

                    // Delete button - only for administrators and scripts with status 'New' or 'Pending Approval'
                    const userRole = '<?php echo e(Auth::user()->role); ?>';
                    const allowedStatuses = ['<?php echo e(ImportFile::STATUS_NEW); ?>',
                        '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>'
                    ];
                    const deleteBtn = (userRole === '<?php echo e(\App\Models\User::ROLE_ADMIN); ?>' && allowedStatuses
                        .includes(data.status)) ? `
            <a href="#" data-id="${data.id}" class="btn btn-sm btn-clean btn-icon delete-script-btn" data-toggle="tooltip" title="Delete Script">
                <i class="menu-icon fas fa-trash"></i>
            </a>` : '';

                    //return button for admin and only if the status is 'signed'
                    const returnBtn = (userRole === '<?php echo e(\App\Models\User::ROLE_ADMIN); ?>' && '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>'.includes(data.status)) ? `

            <a href="#" data-id="${data.id}" data-return-route="${returnRoute}" class="btn btn-sm btn-clean btn-icon return-btn" data-toggle="tooltip" title="Return Script">
                <span class="menu-icon"><i class="fas fa-share"></i></span>
            </a>` : '';
            
                    return downloadBtn + viewBtn + signBtn + returnBtn + deleteBtn + sendBtn ;
                }
            }

        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        params: function() {
                            // Get the current query parameters
                            const query = datatable.getDataSourceQuery();
                            return {
                                signed_date: query.signed_date || '',
                                provider_id: query.provider_id || '',
                                medication_id: query.medication_id || '',
                                clinic_name: query.clinic_name || ''
                            };
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        // Initialize the datatable query parameters with empty values
        datatable.setDataSourceQuery({
            query: {
                signed_date: '',
                provider_id: '',
                medication_id: '',
                clinic_name: ''
            }
        });

        // Function to disable/enable all buttons on the page
        function togglePageButtons(disable) {
            // Disable/enable the download all button
            $('#download-all-global-btn').prop('disabled', disable);
        }

        // Handle ajax done event
        datatable.on('datatable-on-ajax-done', function(e, data) {
            // Check if data is empty
            const isEmpty = !data || !data.length;
            togglePageButtons(isEmpty);
        });

        // Handle ajax fail event
        datatable.on('datatable-on-ajax-fail', function(e, jqXHR) {
            // Disable buttons on error
            togglePageButtons(true);
        });

        let returnScriptId = null;
        $(document).on('click', '.return-btn', function(e) {
            e.preventDefault();
            returnScriptId = $(this).data('id');
            $('#return-reason').val('');
            $('#returnScriptModal').modal('show');
        });

        $('#confirm-return-script-btn').on('click', function() {
            const reason = $('#return-reason').val();
            if (!reason) {
                toastr.error('Please enter a reason for return.');
                return;
            }
            if (!returnScriptId) {
                toastr.error('No script selected.');
                return;
            }
            $.ajax({
                url: "<?php echo e(route('scripts.api.return-script')); ?>",
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    id: returnScriptId,
                    reason: reason
                },
                success: function(res) {
                    toastr.success(res.message || 'Script returned successfully.');
                    $('#returnScriptModal').modal('hide');
                    datatable.reload();
                },
                error: function(xhr) {
                    let message = 'Failed to return script.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    toastr.error(message);
                }
            });
        });

        const routeTemplate = "<?php echo e(route('scripts.download-all-pdf', ['importId' => '__ID__'])); ?>";

        $('#download-all-global-btn').on('click', function() {
            const form = $('<form>', {
                method: 'POST',
                action: routeTemplate.replace('__ID__', '')
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            // Get current filter values and add them to the form
            const signedDate = $('#signed_date_filter').val();
            if (signedDate) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'signed_date',
                    value: signedDate
                }));
            }

            const providerId = $('#provider_filter').val();
            if (providerId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'provider_id',
                    value: providerId
                }));
            }

            const medicationId = $('#medication_filter').val();
            if (medicationId) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'medication_id',
                    value: medicationId
                }));
            }

            const clinicName = $('#clinic_name_filter').val();
            if (clinicName) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'clinic_name',
                    value: clinicName
                }));
            }

            // Get current search value
            const searchValue = $(searchElement).val();
            if (searchValue) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: 'search',
                    value: searchValue
                }));
            }

            $('body').append(form);
            form.submit();
            form.remove();
        });

        $(document).on('click', '.download-single-btn', function(e) {
            e.preventDefault();

            const id = $(this).data('id');
            const url = downloadRoute.replace('::ID', id);

            const form = $('<form>', {
                method: 'POST',
                action: url
            });

            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            $('body').append(form);
            form.submit();
            form.remove();
        });

        // Add event handlers for filter elements
        $('#signed_date_filter, #provider_filter, #medication_filter, #clinic_name_filter').on('change', function() {
            // Get current filter values
            const signedDate = $('#signed_date_filter').val();
            const providerId = $('#provider_filter').val();
            const medicationId = $('#medication_filter').val();
            const clinicName = $('#clinic_name_filter').val();

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                query: {
                    signed_date: signedDate,
                    provider_id: providerId,
                    medication_id: medicationId,
                    clinic_name: clinicName
                }
            });

            // Reload the datatable with the new query parameters
            datatable.reload();
        });

        // Clear date filter
        $('#clear_date_filter').on('click', function() {
            $('#signed_date_filter').val('');

            // Update the query parameters
            const providerId = $('#provider_filter').val();
            const medicationId = $('#medication_filter').val();
            const clinicName = $('#clinic_name_filter').val();

            // Set the query parameters for the datatable
            datatable.setDataSourceQuery({
                query: {
                    signed_date: '',
                    provider_id: providerId,
                    medication_id: medicationId,
                    clinic_name: clinicName
                }
            });

            // Reload the datatable
            datatable.reload();
        });

        // Add event listener for preview buttons
        $(document).on('click', '.preview-btn', function(e) {
            e.preventDefault();

            const fileId = $(this).data('id');
            const viewRoute = $(this).data('view-route');
            const downloadRoute = $(this).data('download-route');
            const sendRoute = $(this).data('send-route');

            // Set the download button URL
            $('#download-preview-btn').attr('href', downloadRoute);

            // Store the file ID for the send button
            $('#send-preview-btn').data('file-id', fileId);

            // Get the row data to determine status
            let rowData = null;
            const allData = datatable.getDataSourceParam('data');

            // Find the row with matching ID
            if (allData && allData.length) {
                rowData = allData.find(item => item.id == fileId);
            }

            // If we couldn't find the row data, try an alternative approach
            if (!rowData) {
                // Try to get the status directly from the DOM
                const statusCell = $(this).closest('tr').find(
                    'td:contains("<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>")');

                // Only enable the send button if we found "<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>" in the row
                if (statusCell.length > 0) {
                    $('#send-preview-btn').removeClass('disabled').prop('disabled', false);
                } else {
                    $('#send-preview-btn').addClass('disabled').prop('disabled', true)
                        .attr('title', 'Cannot send at this stage');
                }
            } else {
                // Enable the send button if status is "<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>"
                if (rowData.status === '<?php echo e(ImportFile::STATUS_PENDING_APPROVAL); ?>') {
                    $('#send-preview-btn').removeClass('disabled').prop('disabled', false);
                } else {
                    $('#send-preview-btn').addClass('disabled').prop('disabled', true)
                        .attr('title', 'Cannot send at this stage');
                }
            }

            // Show the modal
            $('#scriptPreviewModal').modal('show');

            // Load the script preview
            $('#script-preview-content').html(
                '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
            );

            // Load the PDF in an iframe
            setTimeout(function() {
                $('#script-preview-content').html(
                    `<iframe src="${viewRoute}" width="100%" height="100%" style="height: 90vh;" frameborder="0"></iframe>`
                );
            }, 500);
        });

        // Handle send button click in the modal
        $('#send-preview-btn').on('click', function() {
            const fileId = $(this).data('file-id');

            if (!fileId) {
                console.error('No file ID found for sending');
                return;
            }

            // Get the send URL
            const url = sendRoute.replace('::ID', fileId);

            // Close the modal
            $('#scriptPreviewModal').modal('hide');

            // Redirect to the send URL (GET request)
            window.location.href = url;
        });

        // Handle modal events
        $('#scriptPreviewModal').on('hidden.bs.modal', function() {
            // Clear the preview content when modal is closed
            $('#script-preview-content').html('');
        });

        // Handle delete script button click
        $(document).on('click', '.delete-script-btn', function(e) {
            e.preventDefault();

            const scriptId = $(this).data('id');

            Swal.fire({
                title: 'Are you sure?',
                text: "Are you sure you want to delete this script?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes',
                cancelButtonText: 'No'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', scriptId),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        success: function(res) {
                            if (res.status === '1') {
                                toastr.success(res.message);
                                datatable.reload();
                            } else {
                                toastr.error(res.message);
                            }
                        },
                        error: function(xhr) {
                            let message = 'Error deleting script';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                message = xhr.responseJSON.message;
                            }
                            toastr.error(message);
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/scripts/index-all.blade.php ENDPATH**/ ?>