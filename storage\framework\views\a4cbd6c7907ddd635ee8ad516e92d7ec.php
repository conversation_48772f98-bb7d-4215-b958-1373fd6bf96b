<div>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'NPI#','labelRequired' => '1','model' => 'npi','type' => 'number','placeholder' => 'Enter 10-digit NPI number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'NPI#','labelRequired' => '1','model' => 'npi','type' => 'number','placeholder' => 'Enter 10-digit NPI number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'LIC#','labelRequired' => '0','model' => 'lic','placeholder' => 'Enter license number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'LIC#','labelRequired' => '0','model' => 'lic','placeholder' => 'Enter license number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'DEA#','labelRequired' => '0','model' => 'dea','placeholder' => 'Enter DEA number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'DEA#','labelRequired' => '0','model' => 'dea','placeholder' => 'Enter DEA number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/livewire/provider/provider-steps/step2.blade.php ENDPATH**/ ?>