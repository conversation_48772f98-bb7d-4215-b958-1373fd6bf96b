<div>
    @php
        use App\Models\User;
        $user = auth()->user();
    @endphp
    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <x-layout.row>
                        @if ($user->role === User::ROLE_ADMIN || $user->role === User::ROLE_OPERATOR)
                            <x-layout.col>
                                <div wire:ignore class="col" style="padding: 0;">
                                    <label for="provider_id">Provider <span class="text-danger">*</span></label>
                                    <select wire:model="patient.provider_id" id="provider_id" class="form-control">
                                        <option value="">Select Provider</option>
                                        @foreach ($providers as $provider)
                                            <option value="{{ $provider->id }}">{{ $provider->first_name }}
                                                {{ $provider->last_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <x-form.error model="patient.provider_id" />
                            </x-layout.col>
                        @endif

                        <x-layout.col>
                            <x-form.input.text label="First Name" labelRequired="1" model="patient.first_name" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Last Name" labelRequired="1" model="patient.last_name" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="DOB" labelRequired="1" model="patient.dob" type="date"
                                max="9999-12-31" />
                        </x-layout.col>

                        <x-layout.col>
                            <div class="form-group" wire:ignore>
                                <label for="gender" class="form-label">Gender</label>
                                <select wire:model="patient.gender" id="gender" class="form-control gender-select">
                                    <option value="">Select Gender</option>
                                    <option value="M">Male</option>
                                    <option value="F">Female</option>
                                </select>
                            </div>
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Address" labelRequired="1" model="patient.address" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="City" labelRequired="1" model="patient.city" />
                        </x-layout.col>

                        <x-layout.col>
                            <div class="form-group" wire:ignore>
                                <label for="state" class="form-label">State</label>
                                <select wire:model="patient.state_id" id="state" class="form-control gender-select">
                                    <option value="">Select State</option>
                                    @foreach ($states as $state)
                                        <option value="{{ $state->id }}">{{ $state->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Zip Code" labelRequired="1" model="patient.zip_code" />
                        </x-layout.col>

                        <x-layout.col>
                            <x-form.input.text label="Phone" model="patient.phone_number" type="number" />
                        </x-layout.col>
                    </x-layout.row>
                </x-card.body>

                <x-card.footer>
                    <x-btn-with-loading target="store" text="Save" type="submit" />
                </x-card.footer>
            </div>
        </x-layout.row>
    </form>

    @if (session()->has('error-message'))
        <div class="text-danger p-5 mb-3" role="alert">
            {{ session('error-message') }}
            <button type="button" class="close text-danger" onclick="this.parentElement.remove()" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif
</div>

@push('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
@endpush
@push('scripts')
    {{-- <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script> --}}
    <script>
        document.addEventListener("livewire:load", function() {
            function createRoleDropdown() {
                $('#user\\.role').select2({
                    placeholder: "Select Access Type",
                    minimumResultsForSearch: Infinity, // Disable the search functionality
                }).on('change', function(e) {
                    @this.set('user.role', $(e.target).val());
                });
            }

            createRoleDropdown();
            // initializeFlatpickrDobPicker();

            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createRoleDropdown();
            });
        });

        // function initializeFlatpickrDobPicker() {
        //     const dobInput = document.getElementById('dob_date_input');
        //     if (!dobInput) return;

        //     const rawValue = dobInput.value;

        //     flatpickr(dobInput, {
        //         dateFormat: "m/d/Y",
        //         allowInput: true,
        //         clickOpens: true,
        //         maxDate: "today",
        //         defaultDate: rawValue ? (() => {
        //             const [year, month, day] = rawValue.split("-");
        //             return `${month}/${day}/${year}`;
        //         })() : undefined,
        //         onReady: function (selectedDates, dateStr, instance) {
        //             if (!rawValue) {
        //                 instance.currentYear = 1990;
        //                 instance.currentMonth = 0; // January
        //                 instance.redraw();
        //             }
        //         }
        //     });
        // }

        $(document).ready(function() {
            $('#state').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                // Clear validation error immediately when user selects an option
                $(e.target).removeClass('is-invalid');
                $(e.target).closest('.form-group').find('.form-text.text-danger').remove();
                @this.set('patient.state_id', $(e.target).val());
            });

            $('#provider_id').select2({
                placeholder: "Select Provider",
            }).on('change', function(e) {
                // Clear validation error immediately when user selects an option
                $(e.target).removeClass('is-invalid');
                $(e.target).closest('.col').find('.form-text.text-danger').remove();
                @this.set('patient.provider_id', $(e.target).val());
            });

            $('#gender').select2({
                placeholder: "Select Gender",
            }).on('change', function(e) {
                // Clear validation error immediately when user selects an option
                $(e.target).removeClass('is-invalid');
                $(e.target).closest('.form-group').find('.form-text.text-danger').remove();
                @this.set('patient.gender', $(e.target).val());
            });

            // Clear validation errors for regular input fields when user starts typing
            $('input[wire\\:model]').on('input', function() {
                $(this).removeClass('is-invalid');
                $(this).siblings('.form-text.text-danger').remove();
            });
        });
    </script>
@endpush
