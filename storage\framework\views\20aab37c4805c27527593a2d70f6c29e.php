<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'helper'=> false,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'helper'=> false,
]); ?>
<?php foreach (array_filter(([
    'helper'=> false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>
<?php if($helper): ?>
    <span class="form-text text-dark-50"><?php echo $helper; ?></span>
<?php endif; ?><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/components/form/helper.blade.php ENDPATH**/ ?>