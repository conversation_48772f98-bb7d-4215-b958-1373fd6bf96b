<?php

namespace App\Http\Livewire\Practice;

use App\Models\Practice;
use App\Models\State;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class CreateEdit extends Component
{
    public $providers = [];
    public $states = [];
    public $practice;
    public $user;

    public function mount(Practice $practice)
    {
        $this->user = Auth::user();
        $this->practice = $practice;
        $this->loadProvider();
        $this->loadStates();
    }

    public function rules()
    {
        return [
            'practice.name' => 'required',
            'practice.address' => 'required',
            'practice.city' => 'required',
            'practice.state' => 'required',
            'practice.zip' => 'required',
            'practice.phone' => 'required',
            'practice.fax' => 'nullable',
            'practice.default_order_method' => 'required',
            'practice.dispensepro_abbreviation' => 'required_if:practice.default_order_method,Dispense Pro',
        ];
    }

    public function loadProvider()
    {
        $this->providers = User::where('role', User::ROLE_PROVIDER)->get();
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function store()
    {
        $this->validate();

        $this->practice->save();

        session()->flash('success-message', __('messages.practice_created'));

        return redirect()->route('practices.index');
    }

    public function render()
    {
        return view('livewire.practice.create-edit');
    }
}
