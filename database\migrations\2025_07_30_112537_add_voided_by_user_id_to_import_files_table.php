<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('import_files', function (Blueprint $table) {
            $table->foreignId('voided_by_user_id')->nullable()->constrained('users')->nullOnDelete()->cascadeOnUpdate()->after('returned_by_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('import_files', function (Blueprint $table) {
            $table->dropForeign(['voided_by_user_id']);
            $table->dropColumn('voided_by_user_id');
        });
    }
};
